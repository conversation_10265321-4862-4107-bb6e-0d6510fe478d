# :infinity: DevOps

[⏎ Home](../../README.md)

- [Local](#local)
  - [1. Start Supabase](#1-start-supabase)
  - [2. Start `@rackup/api`](#2-start-rackupapi)
  - [3. Start `@rackup/auth`](#3-start-rackupauth)
- [Cloud](#cloud)
  - [Creating a New Environment](#creating-a-new-environment)
    - [1. Create Supabase Database](#1-create-supabase-database)
    - [2. Configure GitHub](#2-configure-github)

## Local

### 1. Start Supabase

```sh
cd dependencies/supabase

pnpm run start
```

### 2. Start `@rackup/api`

```sh
cd packages/api

# Copy sample environment variables
cp .env.sample .env

# Migrate and seed database
pnpm run database:migrate
pnpm run database:seed

pnpm run start
```

### 3. Start `@rackup/auth`

```sh
cd packages/auth

# Copy sample environment variables
cp .env.sample .env

# Seed database
pnpm run database:seed

pnpm run start
```

## Cloud

### Creating a New Environment

#### 1. Create Supabase Database

> [!IMPORTANT]  
> All passwords **must** be generated and stored in 1Password using the following settings:
>
> - **Type:** `Random Password`
> - **Characters:** `40`
> - **Numbers:** `Yes`
> - **Symbols:** `No`

1. Navigate to the [Supabase Dashboard][supabase-dashboard]
2. Select **New project**
   - **Project name:** e.g. `RackUp Development`
   - **Database Password:**
     - Generate and Store in 1Password:
       - **Name:** e.g. `RackUp Development (postgres)`
       - **Username:** `postgres`
       - **Password:** `<Generated Password>`
   - **Region:** `West Europe (London)`
   - **What connections do you plan to use?:** `Only Connection String`
3. Navigate to **SQL Editor** and select **New SQL Snippet**
4. Copy and paste the following SQL

   ```sql
   -- Create user
   create user "prisma" with password '<Generated Password>' bypassrls createdb;

   -- Extend privileges to 'postgres' (necessary to view changes in Supabase Dashboard)
   grant "prisma" to "postgres";

   -- Grant necessary permissions over the relevant schemas (public)
   grant usage on schema public to prisma;
   grant create on schema public to prisma;
   grant all on all tables in schema public to prisma;
   grant all on all routines in schema public to prisma;
   grant all on all sequences in schema public to prisma;
   alter default privileges for role postgres in schema public grant all on tables to prisma;
   alter default privileges for role postgres in schema public grant all on routines to prisma;
   alter default privileges for role postgres in schema public grant all on sequences to prisma;
   ```

   - **Password:**
     - Generate and Store in 1Password:
       - **Name:** e.g. `RackUp Development (prisma)`
       - **Username:** `prisma`
       - **Password:** `<Generated Password>`

5. Select **Run**
6. Under **PRIVATE**, right click on the query and select **Delete query**

#### 2. Configure GitHub

> [!TIP]
> Connection strings can be found in the **Connect** > **ORMs** > **Prisma** section of the [Supabase Dashboard][supabase-dashboard]

> [!WARNING]
> When creating database connection strings in environment secrets, remember to:
>
> - Replace `postgres` user with `prisma`
> - Replace password with `prisma` password generated previously

1. Navigate to the [RackUp GitHub Repository][github-rackup]
2. Navigate to **Settings** > **Environments**
3. Select **New environment**
   - **Name:** e.g. `Development`
4. Select **Add environment secret**

   | Name                  | Type                    | Description                               |
   | --------------------- | ----------------------- | ----------------------------------------- |
   | `ADMIN_PASSWORD`      | ![string][image-string] | Administrator user password               |
   | `DATABASE_DIRECT_URL` | ![string][image-string] | Supabase `DATABASE_URL` connection string |
   | `DATABASE_URL`        | ![string][image-string] | Supabase `DIRECT_URL` connection string   |

5. Select **Add environment variable**

   | Name                            | Type                      | Description                                                                                     |
   | ------------------------------- | ------------------------- | ----------------------------------------------------------------------------------------------- |
   | `AUTHENTICATION_URL`            | ![string][image-string]   | Authentication URL. Should _not_ contain a trailing slash                                       |
   | `DETECT_UNUSED_QUERY_ARGUMENTS` | ![boolean][image-boolean] | [Detect unused query arguments ↗][pothos-plugins-prisma-setup-detecting-unused-query-arguments] |
   | `NODE_ENV`                      | ![string][image-string]   | Node environment                                                                                |

[github-rackup]: https://github.com/hypernova-engineering/rackup
[image-boolean]: ../images/types/boolean.svg
[image-string]: ../images/types/string.svg
[pothos-plugins-prisma-setup-detecting-unused-query-arguments]: https://pothos-graphql.dev/docs/plugins/prisma/setup#detecting-unused-query-arguments
[supabase-dashboard]: https://supabase.com/dashboard
