import eslintJs from "@eslint/js";
import eslintConfigPrettier from "eslint-config-prettier/flat";
import { defineConfig } from "eslint/config";
import globals from "globals";
import typescriptEslint from "typescript-eslint";

const config = defineConfig([
  {
    extends: ["js/recommended"],
    files: ["src/**/*.ts"],
    languageOptions: { globals: globals.node },
    plugins: { js: eslintJs },
  },
  typescriptEslint.configs.recommended,
  /**
   * Prettier must be last
   * @see https://github.com/prettier/eslint-config-prettier#installation
   */
  eslintConfigPrettier,
]);

export default config;
