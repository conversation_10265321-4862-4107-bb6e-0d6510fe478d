import SchemaBuilder from "@pothos/core";
import PothosPluginPrisma from "@pothos/plugin-prisma";
import PothosPluginWithInput from "@pothos/plugin-with-input";
import { DateTimeResolver } from "graphql-scalars";

import type PrismaTypes from "./database/__generated__/graphql-schema";
import { getDatamodel } from "./database/__generated__/graphql-schema";
import { PrismaClient, User } from "./database/__generated__/orm/edge";

const createBuilder = (
  client: PrismaClient,
  onUnusedQuery: "warn" | "error" | null,
) => {
  const builder = new SchemaBuilder<{
    Context: {
      user: {
        payload: User;
      };
    };
    PrismaTypes: PrismaTypes;
    Scalars: {
      DateTime: { Input: Date; Output: Date };
    };
  }>({
    plugins: [PothosPluginPrisma, PothosPluginWithInput],
    prisma: {
      client,
      dmmf: getDatamodel(),
      onUnusedQuery,
    },
  });

  builder.addScalarType("DateTime", DateTimeResolver);

  return builder;
};

export { createBuilder };
