import { PrismaClient, SchemaBuilder } from "../types";

const exercise = (builder: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, client: PrismaClient) => {
  builder.prismaObject("Exercise", {
    fields: (t) => ({
      id: t.exposeID("id"),
      name: t.exposeString("name"),
      createdAt: t.expose("createdAt", { type: "DateTime" }),
      updatedAt: t.expose("updatedAt", { type: "DateTime" }),
    }),
  });

  builder.queryType({
    fields: (t) => ({
      exercises: t.prismaField({
        type: ["Exercise"],
        resolve: (query) => client.exercise.findMany({ ...query }),
      }),
    }),
  });

  builder.mutationType({
    fields: (t) => ({
      createExercise: t.prismaFieldWithInput({
        input: {
          name: t.input.string({ required: true }),
        },
        resolve: (query, parent, args) =>
          client.exercise.create({
            ...query,
            data: {
              name: args.input.name,
            },
          }),
        type: "Exercise",
      }),
    }),
  });
};

export { exercise };
