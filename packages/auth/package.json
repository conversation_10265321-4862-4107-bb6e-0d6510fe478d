{"name": "@rackup/auth", "version": "1.0.0-alpha.0", "private": true, "license": "UNLICENSED", "scripts": {"cloudflare:worker:types": "wrangler types src/types/cloudflare-worker.d.ts", "database:seed": "tsx src/database/seeds/seed.ts", "format:check": "prettier . --check", "format:fix": "prettier . --write", "start": "wrangler dev --env local", "type:check": "tsc --noEmit"}, "dependencies": {"better-auth": "1.3.4", "pg": "8.16.3"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.6.1", "@types/node": "^22", "@types/pg": "8.15.5", "dotenv": "17.2.1", "prettier": "3.6.2", "prettier-plugin-packagejson": "2.5.19", "tsx": "4.20.4", "typescript": "5.9.2", "wrangler": "4.28.1"}, "packageManager": "pnpm@10.14.0", "engines": {"node": "^22"}}